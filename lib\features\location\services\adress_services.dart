import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:http/http.dart' as http;

import 'locaion_services.dart';
import '../../../core/services/firestore_address_service.dart';
import '../../../core/dependency_injection/di_container.dart';
import '../../../core/utils/logger.dart';
import '../../../core/config/environment_config.dart';
import '../../../data/models/adress_model.dart';

class AddressService {
  static final AddressService _instance = AddressService._internal();
  factory AddressService() => _instance;
  AddressService._internal();

  final LocationService _locationService = LocationService();
  FirestoreAddressService? _firestoreService;

  static const double _deliveryRadiusMeters = 100.0;

  // Simple state management
  List<AddressModel> _addresses = [];
  AddressModel? _currentSelectedAddress;
  Position? _lastKnownPosition;
  bool _addressesLoaded = false;

  /// Get Firestore service instance
  FirestoreAddressService get _firestore {
    _firestoreService ??= getIt<FirestoreAddressService>();
    return _firestoreService!;
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _firestore.isUserAuthenticated;

  /// Get current selected address (cached, no location detection)
  AddressModel? get currentSelectedAddress => _currentSelectedAddress;

  /// Get current selected address without location detection (for UI display)
  AddressModel? getCurrentSelectedAddressSync() {
    return _currentSelectedAddress;
  }

  /// Manually set selected address (when user selects from address list)
  void setSelectedAddress(AddressModel address) {
    _currentSelectedAddress = address;
    LogMessage.p('Manually selected address: ${address.addressType}');

    // Notify that address has been manually selected
    _notifyAddressChanged();
  }

  /// Callback to notify when address changes (for LocationBloc)
  Function(AddressModel)? _onAddressChanged;

  /// Set callback for address changes
  void setOnAddressChanged(Function(AddressModel) callback) {
    _onAddressChanged = callback;
  }

  /// Notify listeners that address has changed
  void _notifyAddressChanged() {
    if (_currentSelectedAddress != null && _onAddressChanged != null) {
      _onAddressChanged!(_currentSelectedAddress!);
    }
  }

  /// Get all saved addresses (simple state management)
  Future<List<AddressModel>> getAllAddresses(
      {bool forceRefresh = false}) async {
    if (!isAuthenticated) return [];

    // If already loaded and not forcing refresh, return cached data
    if (_addressesLoaded && !forceRefresh) {
      LogMessage.p('Using cached addresses (${_addresses.length} addresses)');
      return _addresses;
    }

    try {
      LogMessage.p('Fetching addresses from Firestore');
      _addresses = await _firestore.getAllAddresses();
      _addressesLoaded = true;
      return _addresses;
    } catch (e) {
      LogMessage.p('Error fetching addresses: $e');
      return _addresses; // Return what we have, even if empty
    }
  }

  /// Get selected address based on current location (main method)
  Future<AddressModel?> getSelectedAddress() async {
    if (!isAuthenticated) return null;

    // Return cached selected address if available and location hasn't changed much
    if (_currentSelectedAddress != null && _lastKnownPosition != null) {
      final currentPosition = await _locationService.getCurrentPosition();
      if (currentPosition != null) {
        final distance = Geolocator.distanceBetween(
          _lastKnownPosition!.latitude,
          _lastKnownPosition!.longitude,
          currentPosition.latitude,
          currentPosition.longitude,
        );
        // If user hasn't moved more than 100m, return cached result
        if (distance < 100) {
          LogMessage.p(
              'Using cached selected address (moved ${distance.round()}m)');
          return _currentSelectedAddress;
        }
      }
    }

    try {
      // Get current location
      final currentPosition = await _locationService.getCurrentPosition();
      if (currentPosition == null) {
        // Fallback to first saved address if location unavailable
        final addresses = await getAllAddresses();
        return addresses.isNotEmpty ? addresses.first : null;
      }

      // Update last known position
      _lastKnownPosition = currentPosition;

      // Find nearest address within delivery radius
      final nearestAddress =
          await _findNearestAddressWithinRadius(currentPosition);

      if (nearestAddress != null) {
        // Found saved address within delivery radius
        _currentSelectedAddress = nearestAddress;
        return nearestAddress;
      }

      // No saved address within radius - return null
      // UI will show current location details and user can create address
      _currentSelectedAddress = null;
      return null;
    } catch (e) {
      LogMessage.p('Error getting selected address: $e');
      // Fallback to first saved address
      final addresses = await getAllAddresses();
      return addresses.isNotEmpty ? addresses.first : null;
    }
  }

  /// Manually refresh location and selected address (like Zepto's refresh button)
  /// Call this when user taps refresh or when returning from address screens
  Future<AddressModel?> refreshSelectedAddress() async {
    // Clear location cache to force fresh detection
    _lastKnownPosition = null;
    // Don't clear _currentSelectedAddress if user manually selected one

    // Get fresh selected address
    return await getSelectedAddress();
  }

  /// Force refresh addresses from server (call when needed)
  Future<List<AddressModel>> refreshAddresses() async {
    return await getAllAddresses(forceRefresh: true);
  }

  /// Save address to Firestore and update state
  Future<void> saveAddress(AddressModel address) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to save addresses');
    }

    try {
      await _firestore.saveAddress(address);

      // Update local state instead of refetching
      if (_addressesLoaded) {
        final existingIndex = _addresses.indexWhere((a) => a.id == address.id);
        if (existingIndex >= 0) {
          _addresses[existingIndex] = address; // Update existing
        } else {
          _addresses.add(address); // Add new
        }
        LogMessage.p('Updated local address state');
      }

      // Refresh selected address to potentially switch to new address if it's closer
      await getSelectedAddress();
    } catch (e) {
      LogMessage.p('Error saving address: $e');
      rethrow;
    }
  }

  /// Delete address from Firestore and update state
  Future<void> deleteAddress(String addressId) async {
    if (!isAuthenticated) {
      throw Exception('User must be authenticated to delete addresses');
    }

    try {
      await _firestore.deleteAddress(addressId);

      // Update local state instead of refetching
      if (_addressesLoaded) {
        _addresses.removeWhere((address) => address.id == addressId);
        LogMessage.p('Removed address from local state');
      }

      // Refresh selected address
      await getSelectedAddress();
    } catch (e) {
      LogMessage.p('Error deleting address: $e');
      rethrow;
    }
  }

  /// Find nearest saved address within delivery radius (500m)
  Future<AddressModel?> _findNearestAddressWithinRadius(
      Position currentPosition) async {
    final addresses = await getAllAddresses();
    if (addresses.isEmpty) return null;

    AddressModel? nearest;
    double minDistance = double.infinity;

    for (final address in addresses) {
      if (address.latitude != null && address.longitude != null) {
        final distance = Geolocator.distanceBetween(
          currentPosition.latitude,
          currentPosition.longitude,
          address.latitude!.toDouble(),
          address.longitude!.toDouble(),
        );

        // Only consider addresses within delivery radius
        if (distance <= _deliveryRadiusMeters && distance < minDistance) {
          minDistance = distance;
          nearest = address;
        }
      }
    }

    return nearest;
  }

  /// Get distance to selected address (for UI display)
  Future<double?> getDistanceToSelectedAddress() async {
    if (_currentSelectedAddress == null || _lastKnownPosition == null) {
      return null;
    }

    if (_currentSelectedAddress!.latitude != null &&
        _currentSelectedAddress!.longitude != null) {
      return Geolocator.distanceBetween(
        _lastKnownPosition!.latitude,
        _lastKnownPosition!.longitude,
        _currentSelectedAddress!.latitude!.toDouble(),
        _currentSelectedAddress!.longitude!.toDouble(),
      );
    }

    return null;
  }

  /// Check if user needs to go to settings for location permission
  Future<bool> shouldOpenAppSettings() async {
    final permission = await Geolocator.checkPermission();
    return permission == LocationPermission.deniedForever;
  }

  /// Open location settings
  Future<bool> openLocationSettings() async {
    try {
      return await Geolocator.openLocationSettings();
    } catch (e) {
      LogMessage.p('Error opening location settings: $e');
      return false;
    }
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    try {
      return await Geolocator.openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  // Get current position
  Future<Position?> getCurrentPosition() async {
    try {
      // Check if we have permission first
      final hasPermission = await _locationService.checkLocationPermission();
      if (!hasPermission) {
        debugPrint('Location permission not granted');
        return null;
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled');
        return null;
      }

      // Get current position with timeout and error handling
      // Try with high accuracy first, then fall back to medium if it fails
      try {
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
          timeLimit: const Duration(seconds: 10),
        );
      } catch (e) {
        debugPrint('High accuracy failed, trying medium accuracy: $e');
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.medium,
          timeLimit: const Duration(seconds: 15),
        );
      }
    } catch (e) {
      debugPrint('Error getting current position: $e');
      return null;
    }
  }

  // Get address from coordinates
  Future<List<Placemark>?> getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      return await placemarkFromCoordinates(latitude, longitude);
    } catch (e) {
      debugPrint('Error getting address from coordinates: $e');
      return null;
    }
  }

  // Convert Placemark to AddressModel
  AddressModel placemarkToAddressModel(Placemark placemark, Position position,
      {String addressType = 'home', bool isDefault = false}) {
    final id = DateTime.now().millisecondsSinceEpoch.toString();

    final street = placemark.street ?? '';
    final subLocality = placemark.subLocality ?? '';
    final locality = placemark.locality ?? '';
    final administrativeArea = placemark.administrativeArea ?? '';
    final postalCode = placemark.postalCode ?? '';

    // Create address line 1 from street and subLocality
    final addressLine1 =
        [street, subLocality].where((e) => e.isNotEmpty).join(', ');

    // Create full address
    final fullAddress = [
      street,
      subLocality,
      locality,
      administrativeArea,
      postalCode,
    ].where((e) => e.isNotEmpty).join(', ');

    return AddressModel(
      id: id,
      fullAddress: fullAddress,
      addressLine1: addressLine1,
      city: locality,
      state: administrativeArea,
      pincode: postalCode,
      latitude: position.latitude,
      longitude: position.longitude,
      addressType: addressType,
      isDefault: isDefault,
    );
  }

  // Search for addresses based on query
  Future<List<Location>?> searchAddresses(String query) async {
    try {
      return await locationFromAddress(query);
    } catch (e) {
      debugPrint('Error searching addresses: $e');
      return null;
    }
  }

  // Get placemark from location
  Future<List<Placemark>?> getPlacemarkFromLocation(Location location) async {
    try {
      return await placemarkFromCoordinates(
          location.latitude, location.longitude);
    } catch (e) {
      debugPrint('Error getting placemark from location: $e');
      return null;
    }
  }

  // Get place details including building name using Google Places API
  Future<Map<String, dynamic>?> getPlaceDetails(
      double latitude, double longitude) async {
    try {
      final apiKey = EnvironmentConfig.googlePlacesApiKey;

      // First, try to find nearby places using Nearby Search
      final nearbyUrl =
          'https://maps.googleapis.com/maps/api/place/nearbysearch/json'
          '?location=$latitude,$longitude'
          '&radius=50' // 50 meter radius
          '&type=establishment'
          '&key=$apiKey';

      final nearbyResponse = await http.get(Uri.parse(nearbyUrl));

      if (nearbyResponse.statusCode == 200) {
        final nearbyData = json.decode(nearbyResponse.body);
        final results = nearbyData['results'] as List?;

        if (results != null && results.isNotEmpty) {
          // Get the closest place
          final closestPlace = results.first as Map<String, dynamic>;

          // If we have a place_id, get detailed information
          final placeId = closestPlace['place_id'] as String?;
          if (placeId != null) {
            final detailsUrl =
                'https://maps.googleapis.com/maps/api/place/details/json'
                '?place_id=$placeId'
                '&fields=name,formatted_address,types,geometry'
                '&key=$apiKey';

            final detailsResponse = await http.get(Uri.parse(detailsUrl));

            if (detailsResponse.statusCode == 200) {
              final detailsData = json.decode(detailsResponse.body);
              final result = detailsData['result'] as Map<String, dynamic>?;

              if (result != null) {
                return {
                  'name': result['name'] ?? '',
                  'formatted_address': result['formatted_address'] ?? '',
                  'types': result['types'] ?? [],
                  'place_id': placeId,
                };
              }
            }
          }

          // Fallback to nearby search result if details API fails
          return {
            'name': closestPlace['name'] ?? '',
            'formatted_address': closestPlace['vicinity'] ?? '',
            'types': closestPlace['types'] ?? [],
            'place_id': closestPlace['place_id'] ?? '',
          };
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error getting place details: $e');
      return null;
    }
  }

  // Enhanced method to get address with building name
  Future<String> getEnhancedAddress(double latitude, double longitude) async {
    try {
      // Try to get place details first (for building names)
      final placeDetails = await getPlaceDetails(latitude, longitude);

      if (placeDetails != null &&
          placeDetails['name'] != null &&
          placeDetails['name'].toString().isNotEmpty) {
        final placeName = placeDetails['name'].toString();
        final formattedAddress =
            placeDetails['formatted_address']?.toString() ?? '';

        // If we have a meaningful place name (not just an address), use it
        if (placeName.isNotEmpty && !_isAddressLikeName(placeName)) {
          // If we have both name and address, combine them
          if (formattedAddress.isNotEmpty) {
            return '$placeName, $formattedAddress';
          } else {
            // If no formatted address from Places API, get it from geocoding
            final placemarks =
                await getAddressFromCoordinates(latitude, longitude);
            if (placemarks != null && placemarks.isNotEmpty) {
              final geocodedAddress = _buildAddressString(placemarks.first);
              return '$placeName, $geocodedAddress';
            }
            return placeName; // Just return the place name if no address available
          }
        }
      }

      // Fallback to regular geocoding
      final placemarks = await getAddressFromCoordinates(latitude, longitude);
      if (placemarks != null && placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        return _buildAddressString(placemark);
      }

      return 'Address not found';
    } catch (e) {
      debugPrint('Error getting enhanced address: $e');
      // Fallback to basic geocoding even on error
      try {
        final placemarks = await getAddressFromCoordinates(latitude, longitude);
        if (placemarks != null && placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          return _buildAddressString(placemark);
        }
      } catch (fallbackError) {
        debugPrint('Fallback geocoding also failed: $fallbackError');
      }
      return 'Failed to get address';
    }
  }

  // Helper method to check if a place name is just an address
  bool _isAddressLikeName(String name) {
    // Check if the name looks like an address (contains numbers, common address words)
    final addressPatterns = [
      RegExp(r'^\d+'), // Starts with numbers
      RegExp(r'\d+.*\d+'), // Contains multiple numbers
      RegExp(r'(street|road|avenue|lane|drive|way|place|court|circle)',
          caseSensitive: false),
    ];

    return addressPatterns.any((pattern) => pattern.hasMatch(name));
  }

  // Helper method to build address string from placemark
  String _buildAddressString(dynamic placemark) {
    List<String> addressParts = [];

    if (placemark.street != null && placemark.street!.isNotEmpty) {
      addressParts.add(placemark.street!);
    }
    if (placemark.locality != null && placemark.locality!.isNotEmpty) {
      addressParts.add(placemark.locality!);
    }
    if (placemark.administrativeArea != null &&
        placemark.administrativeArea!.isNotEmpty) {
      addressParts.add(placemark.administrativeArea!);
    }
    if (placemark.postalCode != null && placemark.postalCode!.isNotEmpty) {
      addressParts.add(placemark.postalCode!);
    }

    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Unknown location';
  }

  /// Initialize address service (call this when user logs in)
  Future<void> initialize() async {
    if (isAuthenticated) {
      // Load addresses once on app start
      await getAllAddresses();
      // Get initial selected address (this will cache it for UI screens)
      await getSelectedAddress();
      LogMessage.p(
          'AddressService initialized with ${_addresses.length} addresses');
    }
  }

  /// Cleanup resources (call this when user logs out)
  void dispose() {
    _currentSelectedAddress = null;
    _lastKnownPosition = null;
    _addresses.clear();
    _addressesLoaded = false;
    _onAddressChanged = null; // Clear callback to prevent memory leaks
    LogMessage.p('AddressService disposed - all user data cleared');
  }

  /// Reset for new user session
  void reset() {
    dispose();
  }

  /// Get formatted distance string for UI
  String formatDistance(double distanceMeters) {
    if (distanceMeters < 1000) {
      return '${distanceMeters.round()}m away';
    } else {
      final km = distanceMeters / 1000;
      return '${km.toStringAsFixed(1)}km away';
    }
  }

  /// Get delivery radius in meters (for external use)
  static double get deliveryRadius => _deliveryRadiusMeters;
}
